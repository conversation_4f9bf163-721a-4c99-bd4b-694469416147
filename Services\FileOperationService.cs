// **************************************************************************
// 文件名: FileOperationService.cs
// 文件职责: 文件操作服务，负责稿纸的保存和加载功能
// 功能描述: 复刻源项目FileOperationsManager的核心功能到WPF架构
// 更新日期: 2025-7-26
// 版本编号: 1.0.0
// 作者: Augment Agent
// 代码行数限制: ≤ 500行（Service类限制）
// **************************************************************************

using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using 计算稿纸.Models;
using 计算稿纸.Core;

namespace 计算稿纸.Services
{
    /// <summary>
    /// 文件操作服务
    /// 负责稿纸的保存、加载和文件管理功能
    /// </summary>
    public class FileOperationService : IDisposable
    {
        #region 私有字段

        private readonly string _dataDirectory;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly HistoryManager _historyManager;
        private bool _disposed;

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化文件操作服务
        /// </summary>
        /// <param name="historyManager">历史记录管理器</param>
        public FileOperationService(HistoryManager historyManager)
        {
            _historyManager = historyManager ?? throw new ArgumentNullException(nameof(historyManager));
            
            // 设置数据目录
            _dataDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "计算稿纸", "SavedSheets");
            
            // 确保目录存在
            if (!Directory.Exists(_dataDirectory))
            {
                Directory.CreateDirectory(_dataDirectory);
            }

            // 配置JSON序列化选项
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            };
        }

        #endregion

        #region 稿纸保存功能

        /// <summary>
        /// 保存稿纸到文件（复刻源项目FileOperationsManager.SaveCurrentSheetAsync逻辑）
        /// </summary>
        /// <param name="sheet">要保存的稿纸</param>
        /// <param name="fileName">文件名（不包含扩展名）</param>
        /// <returns>保存是否成功</returns>
        public async Task<bool> SaveSheetAsync(CalculationSheet sheet, string fileName)
        {
            try
            {
                if (sheet == null || string.IsNullOrWhiteSpace(fileName))
                    return false;

                // 更新最后修改时间
                sheet.LastModifiedTime = DateTime.Now;
                
                // 构建文件路径
                string filePath = Path.Combine(_dataDirectory, $"{fileName}.json");
                
                // 序列化为JSON
                string jsonContent = JsonSerializer.Serialize(sheet, _jsonOptions);
                
                // 写入文件
                await File.WriteAllTextAsync(filePath, jsonContent);
                
                // 添加到历史记录（复刻源项目逻辑）
                AddToHistory(sheet, fileName);
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"保存稿纸失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 从文件加载稿纸
        /// </summary>
        /// <param name="fileName">文件名（不包含扩展名）</param>
        /// <returns>加载的稿纸，失败时返回null</returns>
        public async Task<CalculationSheet?> LoadSheetAsync(string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return null;

                // 处理示例数据（任务4.1：记录项恢复功能实现）
                if (fileName.StartsWith("sample_"))
                {
                    return CreateSampleSheet(fileName);
                }

                // 构建文件路径
                string filePath = Path.Combine(_dataDirectory, $"{fileName}.json");

                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    return null;
                }

                // 读取文件内容
                string jsonContent = await File.ReadAllTextAsync(filePath);

                // 反序列化
                CalculationSheet? sheet = JsonSerializer.Deserialize<CalculationSheet>(jsonContent, _jsonOptions);

                return sheet;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载稿纸失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建示例稿纸数据（用于测试恢复功能）
        /// </summary>
        /// <param name="fileName">示例文件名</param>
        /// <returns>示例稿纸数据</returns>
        private CalculationSheet CreateSampleSheet(string fileName)
        {
            var sheet = new CalculationSheet
            {
                Id = Guid.NewGuid().ToString(),
                Name = $"示例稿纸_{fileName}",
                CreatedTime = DateTime.Now.AddDays(-1),
                LastModifiedTime = DateTime.Now.AddHours(-2),
                Items = new System.Collections.ObjectModel.ObservableCollection<CalculationItem>()
            };

            // 根据不同的示例文件创建不同的计算项
            switch (fileName)
            {
                case "sample_001":
                    sheet.Items.Add(new CalculationItem { Expression = "「长」12.5*「宽」8.3", Result = "103.75", Note = "A-F轴墙皮开打面积" });
                    sheet.Items.Add(new CalculationItem { Expression = "103.75*「厚度」0.24", Result = "24.9", Note = "体积计算" });
                    break;
                case "sample_002":
                    sheet.Items.Add(new CalculationItem { Expression = "「基数」250718-1017", Result = "249701", Note = "大风风光计算" });
                    break;
                default:
                    sheet.Items.Add(new CalculationItem { Expression = $"示例计算_{fileName}", Result = "100", Note = "示例备注" });
                    break;
            }

            return sheet;
        }

        #endregion

        #region 历史记录集成

        /// <summary>
        /// 将保存的稿纸添加到历史记录中
        /// </summary>
        /// <param name="sheet">稿纸数据</param>
        /// <param name="fileName">文件名</param>
        private void AddToHistory(CalculationSheet sheet, string fileName)
        {
            try
            {
                // 创建历史记录项（复刻源项目逻辑）
                var historyItem = new Core.HistoryItem(sheet.Name, fileName)
                {
                    Timestamp = sheet.CreatedTime
                };

                // 添加到历史管理器
                _historyManager.AddHistory(sheet.Name, fileName);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加历史记录失败: {ex.Message}");
            }
        }

        #endregion

        #region 文件管理

        /// <summary>
        /// 删除稿纸文件
        /// </summary>
        /// <param name="fileName">文件名（不包含扩展名）</param>
        /// <returns>删除是否成功</returns>
        public bool DeleteSheet(string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return false;

                string filePath = Path.Combine(_dataDirectory, $"{fileName}.json");

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除稿纸失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 异步删除稿纸文件（任务4.3：撤回数据恢复机制）
        /// </summary>
        /// <param name="fileName">文件名（不包含扩展名）</param>
        /// <returns>删除是否成功</returns>
        public async Task<bool> DeleteSheetAsync(string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return false;

                // 对于示例数据，不执行实际删除
                if (fileName.StartsWith("sample_"))
                {
                    return true; // 模拟删除成功
                }

                string filePath = Path.Combine(_dataDirectory, $"{fileName}.json");

                if (File.Exists(filePath))
                {
                    await Task.Run(() => File.Delete(filePath));
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"异步删除稿纸失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查稿纸文件是否存在
        /// </summary>
        /// <param name="fileName">文件名（不包含扩展名）</param>
        /// <returns>文件是否存在</returns>
        public bool SheetExists(string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return false;

                string filePath = Path.Combine(_dataDirectory, $"{fileName}.json");
                return File.Exists(filePath);
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 资源释放

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源的具体实现
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                // 释放托管资源
                _disposed = true;
            }
        }

        #endregion
    }
}

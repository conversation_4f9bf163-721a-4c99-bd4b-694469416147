﻿++解决方案 '计算稿纸' ‎ (1 个项目，共 1 个)
i:{00000000-0000-0000-0000-000000000000}:计算稿纸.sln
++计算稿纸
i:{00000000-0000-0000-0000-000000000000}:计算稿纸
++依赖项
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:>3009
++Behaviors
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\behaviors\
++Converters
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\converters\
++Core
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\core\
++Helpers
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\helpers\
++Models
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\models\
++Resources
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\
++Services
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\services\
++ViewModels
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\
++Views
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\
++过程任务文档
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\过程任务文档\
++开发任务文档
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\开发任务文档\
++其他文档
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\其他文档\
++App.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\app.xaml
++AssemblyInfo.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\assemblyinfo.cs
++MainWindow.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\mainwindow.xaml
++包
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:>3068
++分析器
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:>3056
++框架
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:>3065
++BottomContainerBehavior.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\behaviors\bottomcontainerbehavior.cs
++HistoryAnimationBehavior.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\behaviors\historyanimationbehavior.cs
++README.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\behaviors\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\converters\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\core\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\helpers\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\models\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\services\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\过程任务文档\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\开发任务文档\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\dictionaries\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\styles\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\templates\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\services\interfaces\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\base\readme.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\controls\readme.md
++FormulaEngine.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\core\formulaengine.cs
++HistoryManager.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\core\historymanager.cs
++CalculationPanelHelper.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\helpers\calculationpanelhelper.cs
++CalculationItem.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\models\calculationitem.cs
++CalculationSheet.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\models\calculationsheet.cs
++Dictionaries
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\dictionaries\
++Images
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\
++Styles
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\styles\
++Templates
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\templates\
++LogConfig.json
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\logconfig.json
++Interfaces
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\services\interfaces\
++BottomContainerService.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\services\bottomcontainerservice.cs
++CalculationPanelManager.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\services\calculationpanelmanager.cs
++ContainerMeasurementService.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\services\containermeasurementservice.cs
++DynamicCharacterWrappingService.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\services\dynamiccharacterwrappingservice.cs
++LoggingService.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\services\loggingservice.cs
++Base
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\base\
++AboutViewModel.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\aboutviewmodel.cs
++CalculationViewModel.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\calculationviewmodel.cs
++HistoryViewModel.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\historyviewmodel.cs
++MainViewModel.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\mainviewmodel.cs
++SettingsViewModel.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\settingsviewmodel.cs
++Controls
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\controls\
++AboutWindow.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\aboutwindow.xaml
++CustomToastMessage.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\customtoastmessage.xaml
++HistoryWindow.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\historywindow.xaml
++SettingsWindow.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\settingswindow.xaml
++UniversalDialog.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\universaldialog.xaml
++HandyControl迁移实施方案.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\过程任务文档\handycontrol迁移实施方案.md
++当前任务推进工作.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\过程任务文档\当前任务推进工作.md
++任务交接文档.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\过程任务文档\任务交接文档.md
++修复备注功能文档.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\过程任务文档\修复备注功能文档.md
++增强版日志系统设计.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\过程任务文档\增强版日志系统设计.md
++WPF的开发任务文档.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\开发任务文档\wpf的开发任务文档.md
++计划转WPF架构.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\开发任务文档\计划转wpf架构.md
++测试输出信息文档.txt
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\其他文档\测试输出信息文档.txt
++错误信息.txt
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\其他文档\错误信息.txt
++多括号格式功能验证.md
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\其他文档\多括号格式功能验证.md
++App.xaml.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\app.xaml.cs
++MainWindow.xaml.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\mainwindow.xaml.cs
++HandyControl (3.4.0)
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:>3070
++MahApps.Metro (2.4.10)
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:>3069
++MahApps.Metro.IconPacks.Material (4.11.0)
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:>3071
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:c:\program files\dotnet\sdk\9.0.101\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++Microsoft.Interop.ComInterfaceGenerator
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.interop.cominterfacegenerator.dll
++Microsoft.Interop.JavaScript.JSImportGenerator
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.interop.javascript.jsimportgenerator.dll
++Microsoft.Interop.LibraryImportGenerator
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.interop.libraryimportgenerator.dll
++Microsoft.Interop.SourceGeneration
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\microsoft.interop.sourcegeneration.dll
++System.Text.Json.SourceGeneration
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++System.Text.RegularExpressions.Generator
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\8.0.11\analyzers\dotnet\cs\system.text.regularexpressions.generator.dll
++Microsoft.NETCore.App
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:>3067
++Microsoft.WindowsDesktop.App.WPF
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:>3066
++Animations.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\dictionaries\animations.xaml
++Brushes.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\dictionaries\brushes.xaml
++Colors.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\dictionaries\colors.xaml
++app图标
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\app图标\
++标题栏
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\标题栏\
++功能类
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\功能类\
++ControlStyles.xaml
++BaseViewModel.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\base\baseviewmodel.cs
++RelayCommand.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\viewmodels\base\relaycommand.cs
++CalculationPanel.xaml
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\controls\calculationpanel.xaml
++AboutWindow.xaml.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\aboutwindow.xaml.cs
++CustomToastMessage.xaml.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\customtoastmessage.xaml.cs
++HistoryWindow.xaml.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\historywindow.xaml.cs
++SettingsWindow.xaml.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\settingswindow.xaml.cs
++UniversalDialog.xaml.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\universaldialog.xaml.cs
++计算稿纸.ico
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\app图标\计算稿纸.ico
++计算稿纸.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\app图标\计算稿纸.png
++固定窗体.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\标题栏\固定窗体.png
++关闭.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\标题栏\关闭.png
++截图.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\标题栏\截图.png
++最大化.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\标题栏\最大化.png
++最小化.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\标题栏\最小化.png
++保存.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\功能类\保存.png
++备注.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\功能类\备注.png
++撤销.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\功能类\撤销.png
++历史.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\功能类\历史.png
++清空.png
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\resources\images\功能类\清空.png
++CalculationPanel.xaml.cs
i:{42ae7db0-de3d-4f9c-a837-a93fac87f7a4}:e:\~2-计算稿纸\计算稿纸\views\controls\calculationpanel.xaml.cs

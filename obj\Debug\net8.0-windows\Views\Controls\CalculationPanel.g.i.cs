﻿#pragma checksum "..\..\..\..\..\Views\Controls\CalculationPanel.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B007D2A941D06C2C635350417B995BAE5AD75F1A"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using HandyControl.Controls;
using HandyControl.Data;
using HandyControl.Expression.Media;
using HandyControl.Expression.Shapes;
using HandyControl.Interactivity;
using HandyControl.Media.Animation;
using HandyControl.Media.Effects;
using HandyControl.Properties.Langs;
using HandyControl.Themes;
using HandyControl.Tools;
using HandyControl.Tools.Converter;
using HandyControl.Tools.Extension;
using MahApps.Metro.IconPacks;
using MahApps.Metro.IconPacks.Converter;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace 计算稿纸.Views.Controls {
    
    
    /// <summary>
    /// CalculationPanel
    /// </summary>
    public partial class CalculationPanel : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 97 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal HandyControl.Controls.TextBox TextEditor;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PlaceholderContainer;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PlaceholderCursor;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PlaceholderText;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid EqualsResultGrid;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NoteDisplayPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/计算稿纸;component/views/controls/calculationpanel.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TextEditor = ((HandyControl.Controls.TextBox)(target));
            
            #line 99 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
            this.TextEditor.GotFocus += new System.Windows.RoutedEventHandler(this.TextEditor_GotFocus);
            
            #line default
            #line hidden
            
            #line 100 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
            this.TextEditor.LostFocus += new System.Windows.RoutedEventHandler(this.TextEditor_LostFocus);
            
            #line default
            #line hidden
            
            #line 101 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
            this.TextEditor.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TextEditor_TextChanged);
            
            #line default
            #line hidden
            
            #line 102 "..\..\..\..\..\Views\Controls\CalculationPanel.xaml"
            this.TextEditor.KeyDown += new System.Windows.Input.KeyEventHandler(this.TextEditor_KeyDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.PlaceholderContainer = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 3:
            this.PlaceholderCursor = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.PlaceholderText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.EqualsResultGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.NoteDisplayPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}


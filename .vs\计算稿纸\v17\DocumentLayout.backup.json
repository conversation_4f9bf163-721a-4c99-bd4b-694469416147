{"Version": 1, "WorkspaceRootPath": "E:\\~2-计算稿纸\\计算稿纸\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|e:\\~2-计算稿纸\\计算稿纸\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|e:\\~2-计算稿纸\\计算稿纸\\core\\historymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|solutionrelative:core\\historymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|e:\\~2-计算稿纸\\计算稿纸\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|e:\\~2-计算稿纸\\计算稿纸\\viewmodels\\historyviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|solutionrelative:viewmodels\\historyviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|e:\\~2-计算稿纸\\计算稿纸\\services\\calculationpanelmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|solutionrelative:services\\calculationpanelmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|e:\\~2-计算稿纸\\计算稿纸\\views\\controls\\calculationpanel.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|solutionrelative:views\\controls\\calculationpanel.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|e:\\~2-计算稿纸\\计算稿纸\\services\\fileoperationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|solutionrelative:services\\fileoperationservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|e:\\~2-计算稿纸\\计算稿纸\\views\\historywindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|solutionrelative:views\\historywindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|e:\\~2-计算稿纸\\计算稿纸\\views\\customtoastmessage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{42AE7DB0-DE3D-4F9C-A837-A93FAC87F7A4}|计算稿纸.csproj|solutionrelative:views\\customtoastmessage.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml", "DocumentMoniker": "E:\\~2-计算稿纸\\计算稿纸\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "E:\\~2-计算稿纸\\计算稿纸\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-24T08:26:52.7Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "CustomToastMessage.xaml", "DocumentMoniker": "E:\\~2-计算稿纸\\计算稿纸\\Views\\CustomToastMessage.xaml", "RelativeDocumentMoniker": "Views\\CustomToastMessage.xaml", "ToolTip": "E:\\~2-计算稿纸\\计算稿纸\\Views\\CustomToastMessage.xaml", "RelativeToolTip": "Views\\CustomToastMessage.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-26T10:15:28.063Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "HistoryWindow.xaml", "DocumentMoniker": "E:\\~2-计算稿纸\\计算稿纸\\Views\\HistoryWindow.xaml", "RelativeDocumentMoniker": "Views\\HistoryWindow.xaml", "ToolTip": "E:\\~2-计算稿纸\\计算稿纸\\Views\\HistoryWindow.xaml", "RelativeToolTip": "Views\\HistoryWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-26T12:22:45.719Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:0:0:{eefa5220-e298-11d0-8f78-00a0c9110057}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Document", "DocumentIndex": 6, "Title": "FileOperationService.cs", "DocumentMoniker": "E:\\~2-计算稿纸\\计算稿纸\\Services\\FileOperationService.cs", "RelativeDocumentMoniker": "Services\\FileOperationService.cs", "ToolTip": "E:\\~2-计算稿纸\\计算稿纸\\Services\\FileOperationService.cs", "RelativeToolTip": "Services\\FileOperationService.cs", "ViewState": "AgIAAIgAAAAAAAAAAAApwJ0AAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T13:46:22.529Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "CalculationPanel.xaml.cs", "DocumentMoniker": "E:\\~2-计算稿纸\\计算稿纸\\Views\\Controls\\CalculationPanel.xaml.cs", "RelativeDocumentMoniker": "Views\\Controls\\CalculationPanel.xaml.cs", "ToolTip": "E:\\~2-计算稿纸\\计算稿纸\\Views\\Controls\\CalculationPanel.xaml.cs", "RelativeToolTip": "Views\\Controls\\CalculationPanel.xaml.cs", "ViewState": "AgIAAGYCAAAAAAAAAAASwIwCAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T13:37:26.793Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "CalculationPanelManager.cs", "DocumentMoniker": "E:\\~2-计算稿纸\\计算稿纸\\Services\\CalculationPanelManager.cs", "RelativeDocumentMoniker": "Services\\CalculationPanelManager.cs", "ToolTip": "E:\\~2-计算稿纸\\计算稿纸\\Services\\CalculationPanelManager.cs", "RelativeToolTip": "Services\\CalculationPanelManager.cs", "ViewState": "AgIAAN8BAAAAAAAAAAAvwAYBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T13:08:17.021Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "HistoryViewModel.cs", "DocumentMoniker": "E:\\~2-计算稿纸\\计算稿纸\\ViewModels\\HistoryViewModel.cs", "RelativeDocumentMoniker": "ViewModels\\HistoryViewModel.cs", "ToolTip": "E:\\~2-计算稿纸\\计算稿纸\\ViewModels\\HistoryViewModel.cs", "RelativeToolTip": "ViewModels\\HistoryViewModel.cs", "ViewState": "AgIAALsCAABgoWEK50YowGsCAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T12:54:00.877Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "E:\\~2-计算稿纸\\计算稿纸\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "E:\\~2-计算稿纸\\计算稿纸\\MainWindow.xaml.cs", "RelativeToolTip": "MainWindow.xaml.cs", "ViewState": "AgIAALICAAAAAAAAAAAtwJMCAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T12:44:20.9Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "HistoryManager.cs", "DocumentMoniker": "E:\\~2-计算稿纸\\计算稿纸\\Core\\HistoryManager.cs", "RelativeDocumentMoniker": "Core\\HistoryManager.cs", "ToolTip": "E:\\~2-计算稿纸\\计算稿纸\\Core\\HistoryManager.cs", "RelativeToolTip": "Core\\HistoryManager.cs", "ViewState": "AgIAAN0AAAAAAAAAAAAiwP4AAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T12:43:42.648Z", "EditorCaption": ""}]}]}]}
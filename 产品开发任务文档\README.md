# 开发任务文档目录

## 📋 **文档说明**

本文件夹存放项目的**核心开发指导文档**，包括：
- 长期有效的开发规范和标准
- 项目架构设计和技术选型
- 开发流程和质量标准
- 核心功能的设计文档

## 🎯 **文档特点**
- **持久性**：长期有效，不会因任务完成而过时
- **指导性**：为开发工作提供标准和规范
- **权威性**：项目开发的重要参考依据
- **稳定性**：内容相对稳定，不频繁变更

## 📁 **当前文档列表**

### 🏗️ **架构设计文档**
- `WPF的开发任务文档.md` - WPF项目开发的核心指导文档
- `计划转WPF架构.md` - WPF架构转换的总体规划

## 🚨 **重要约束**
- **禁止添加过程性文档**：临时性、过程性文档请放入`过程任务文档`文件夹
- **只保留核心文档**：只存放对项目开发有长期指导意义的文档
- **定期审查**：定期检查文档的有效性和必要性

## 📝 **文档管理原则**
1. **核心优先**：只保留最核心的开发指导文档
2. **避免冗余**：不重复存放相似内容的文档
3. **保持更新**：确保文档内容与项目现状一致
4. **清晰分类**：按功能和用途明确分类